namespace HWSAuditPlatform.Domain.Enums;

/// <summary>
/// Represents the frequency type for recurring audit schedules.
/// Maps to the frequency_type enum in the database.
/// </summary>
public enum FrequencyType
{
    /// <summary>
    /// Daily frequency
    /// </summary>
    Daily,

    /// <summary>
    /// Weekly frequency
    /// </summary>
    Weekly,

    /// <summary>
    /// Monthly frequency
    /// </summary>
    Monthly,

    /// <summary>
    /// Yearly frequency
    /// </summary>
    Yearly,

    /// <summary>
    /// Custom frequency (note: typo in original DBML - COSTUM)
    /// </summary>
    Custom
}
