# HWSAuditPlatform.Domain

## Overview

The **HWSAuditPlatform.Domain** project represents the core business logic layer of the HWS Audit Platform. This layer contains the essential business entities, rules, and domain logic that define how the audit management system operates, independent of any infrastructure or application concerns.

Built following **Domain-Driven Design (DDD)** principles, this layer serves as the heart of the audit management system, encapsulating:

- **Business Entities**: Core domain objects like Audits, Templates, Users, and Findings
- **Business Rules**: Domain logic and constraints that ensure data integrity and business process compliance
- **Domain Events**: Events that represent significant business occurrences
- **Value Objects**: Immutable objects that represent domain concepts
- **Enumerations**: Strongly-typed constants representing business states and types

The domain layer is designed to be:
- **Technology Agnostic**: No dependencies on databases, frameworks, or external services
- **Rich in Business Logic**: Contains the core rules and behaviors of the audit domain
- **Event-Driven**: Supports domain events for loose coupling and extensibility
- **Testable**: Pure business logic that can be easily unit tested

## Project Structure

```
src/HWSAuditPlatform.Domain/
├── Common/                     # Base classes and interfaces
│   ├── BaseEntity.cs          # Base entity with common properties
│   ├── AuditableEntity.cs     # Entity with audit tracking and versioning
│   ├── IAggregateRoot.cs      # Marker interface for aggregate roots
│   └── IDomainEvent.cs        # Interface for domain events
├── Entities/                   # Domain entities organized by functional areas
│   ├── Users/                 # User management entities
│   ├── Organization/          # Organizational structure entities
│   ├── Templates/             # Audit template and question entities
│   ├── Audits/               # Audit execution entities
│   ├── Findings/             # Finding and corrective action entities
│   ├── Scheduling/           # Recurring audit scheduling entities
│   └── Workflow/             # Workflow and logging entities
├── Enums/                     # Domain enumerations
├── ValueObjects/              # Value objects for domain concepts
├── Events/                    # Domain events
├── Exceptions/                # Domain-specific exceptions
└── README.md                  # This documentation file
```

### Directory Purposes

#### **Common/**
Contains base classes and interfaces that provide common functionality across all domain entities:
- **BaseEntity**: Provides common properties like Id, CreatedAt, UpdatedAt, and domain event handling
- **AuditableEntity**: Extends BaseEntity with optimistic concurrency control and audit tracking
- **IAggregateRoot**: Marker interface identifying entities that serve as aggregate roots
- **IDomainEvent**: Interface for implementing domain events

#### **Entities/**
Contains the core business entities organized by functional areas. Each entity represents a key business concept and encapsulates related business logic and rules.

#### **Enums/**
Strongly-typed enumerations that represent business states, types, and categories. These map directly to database enums and ensure type safety throughout the application.

#### **ValueObjects/**
Immutable objects that represent domain concepts without identity. Examples include Address, AuditScore, and other composite values.

#### **Events/**
Domain events that represent significant business occurrences. These enable loose coupling between aggregates and support event-driven architecture.

#### **Exceptions/**
Domain-specific exceptions that represent business rule violations and domain-related errors.

## Entity Relationships

The domain model is organized into seven functional areas, each representing a key aspect of the audit management system:

### Core Access & Users

**Purpose**: Manages user identities, roles, and group memberships with Active Directory integration.

**Key Entities**:
- **Role**: Defines user roles (Admin, Manager, Auditor) with associated permissions
- **User**: Represents system users (all AD-synced) with profile information
- **UserGroup**: Collections of users for audit assignment purposes (can be AD-synced or local)
- **UserGroupMember**: Many-to-many relationship between users and groups
- **AdGroupRoleMapping**: Maps Active Directory groups to application roles

**Relationships**:
```
Role (1) ←→ (N) User
User (N) ←→ (N) UserGroup (via UserGroupMember)
Role (1) ←→ (N) AdGroupRoleMapping
User (N) ←→ (1) Factory [optional primary factory]
```

### Organizational Structure

**Purpose**: Defines the physical and logical hierarchy where audits are conducted.

**Key Entities**:
- **Location**: Geographical locations (countries, regions)
- **Factory**: Specific plants or sites within locations
- **Area**: Departments or sections within factories
- **SubArea**: Granular divisions within areas

**Relationships**:
```
Location (1) ←→ (N) Factory
Factory (1) ←→ (N) Area
Area (1) ←→ (N) SubArea
```

### Audit Definition & Templates

**Purpose**: Defines the structure, content, and rules of audit templates.

**Key Entities**:
- **AuditTemplate**: Reusable blueprints for conducting audits
- **QuestionGroup**: Logical sections to organize questions
- **Question**: Individual questions with types, validation, and conditional logic
- **QuestionOption**: Predefined answer choices for select-type questions

**Relationships**:
```
AuditTemplate (1) ←→ (N) QuestionGroup
AuditTemplate (1) ←→ (N) Question
QuestionGroup (1) ←→ (N) Question
Question (1) ←→ (N) QuestionOption
Question (1) ←→ (N) Question [parent-child for conditional questions]
```

### Audit Execution & Evidence

**Purpose**: Captures data from performed audits including answers and evidence.

**Key Entities**:
- **Audit**: Specific instances of audits being performed
- **AuditAnswer**: Responses provided by auditors to questions
- **AuditAnswerSelectedOption**: Links answers to selected options (multi-select)
- **AuditAnswerFailureReason**: Captures reasons for failed answers
- **AuditAttachment**: Files attached as evidence to answers

**Relationships**:
```
Audit (1) ←→ (N) AuditAnswer
AuditAnswer (1) ←→ (N) AuditAnswerSelectedOption
AuditAnswer (1) ←→ (N) AuditAnswerFailureReason
AuditAnswer (1) ←→ (N) AuditAttachment
Audit (N) ←→ (1) AuditTemplate
Audit (N) ←→ (1) Factory/Area/SubArea
```

### Findings & Corrective Actions

**Purpose**: Manages non-conformities identified during audits and their resolution.

**Key Entities**:
- **Finding**: Records of deviations or non-conformities
- **CorrectiveAction**: Actions planned to address findings

**Relationships**:
```
Finding (1) ←→ (N) CorrectiveAction
Finding (N) ←→ (1) AuditAnswer
```

### Scheduling & Recurrence

**Purpose**: Manages automated scheduling of recurring audits.

**Key Entities**:
- **RecurringAuditSetting**: Defines recurring audit configurations
- **RecurrenceRule**: Detailed frequency and pattern specifications

**Relationships**:
```
RecurringAuditSetting (1) ←→ (1) RecurrenceRule
RecurringAuditSetting (1) ←→ (N) Audit [generated audits]
```

### Workflow & Logging

**Purpose**: Manages audit correction workflows and system-wide event tracking.

**Key Entities**:
- **AuditCorrectionRequest**: Requests to modify submitted audits
- **AuditLog**: System-wide audit trail of events and changes

**Relationships**:
```
AuditCorrectionRequest (N) ←→ (1) Audit
AuditLog (N) ←→ (1) User [optional]
```

## Key Design Patterns

### Aggregate Roots

Entities implementing `IAggregateRoot` serve as consistency boundaries and entry points for business operations:

- **User**: Manages user identity and group memberships
- **Factory**: Manages organizational hierarchy
- **AuditTemplate**: Manages template structure and questions
- **Audit**: Manages audit execution and answers
- **Finding**: Manages findings and corrective actions
- **RecurringAuditSetting**: Manages recurring audit schedules

### Domain Events

The domain uses events to decouple business logic and enable reactive behaviors:

- Events are raised by aggregate roots when significant business events occur
- Events implement `IDomainEvent` interface
- Events are collected in entities and can be processed by application services

### Base Entities

**BaseEntity<TKey>**: Provides common functionality for all entities:
- Primary key management
- Created/Updated timestamps
- Domain event collection and management
- Equality comparison based on identity

**AuditableEntity<TKey>**: Extends BaseEntity with:
- Optimistic concurrency control via `RecordVersion`
- Audit tracking with `CreatedByUserId` and `UpdatedByUserId`
- Support for offline synchronization scenarios

### Value Objects

Immutable objects representing domain concepts:
- **Address**: Represents physical addresses
- **AuditScore**: Represents audit scoring with percentage calculations
- Inherit from `ValueObject` base class with proper equality semantics

## Enumerations

The domain defines several enumerations that map to database enums:

### Core Enums
- **UserRole**: Admin, Manager, Auditor
- **QuestionType**: YesNo, Numeric, SingleSelect, MultiSelect, ShortText, LongText, Date
- **SeverityLevel**: Critical, Major, Minor, Observation

### Status Enums
- **AuditOverallStatus**: Scheduled, InProgress, Submitted, PendingManagerReview, PendingCorrection, ManagerReviewed, Closed, Cancelled
- **FindingStatus**: Open, UnderInvestigation, PendingCorrectiveAction, PendingVerification, Closed, Rejected
- **CorrectiveActionStatus**: Assigned, InProgress, CompletedPendingVerification, VerifiedClosed, Cancelled, Ineffective
- **CorrectionRequestStatus**: PendingApproval, Approved, Denied, ChangesSubmitted

### Assignment and Scheduling Enums
- **AssignmentType**: Individual, GroupAny, GroupAllScheduled
- **FrequencyType**: DAILY, WEEKLY, MONTHLY, YEARLY, COSTUM
- **EvidenceTimingHint**: OnSitePreferred, LaterAllowed, AnyTime

## Domain Events

The domain implements an event-driven architecture with the following key events:

### Audit Events
- **AuditCreatedEvent**: Raised when a new audit is created
- **AuditStatusChangedEvent**: Raised when audit status changes
- **AuditSubmittedEvent**: Raised when an audit is submitted
- **AuditAssignedEvent**: Raised when an audit is assigned to a user
- **AuditStartedEvent**: Raised when an auditor starts working on an audit

### Finding Events
- **FindingCreatedEvent**: Raised when a new finding is identified
- **FindingStatusChangedEvent**: Raised when finding status changes
- **CorrectiveActionCreatedEvent**: Raised when a corrective action is created
- **CorrectiveActionStatusChangedEvent**: Raised when corrective action status changes
- **CorrectiveActionCompletedEvent**: Raised when a corrective action is completed

## Business Rules

The domain enforces several important business rules:

### Audit Rules
- Audits can only be started by assigned users or group members
- Audit status transitions must follow defined workflow
- Submitted audits cannot be modified without correction requests
- Evidence is required for questions marked as `EvidenceRequired`

### Assignment Rules
- Individual assignments require a specific user
- Group assignments allow any group member to claim the audit
- GroupAllScheduled creates individual audits for each group member

### Template Rules
- Only published and active templates can be used for new audits
- Question conditional logic must reference valid parent questions
- Question options are only valid for SingleSelect/MultiSelect types

### Finding Rules
- Findings must be linked to audit answers
- Corrective actions must have valid due dates
- Finding severity affects escalation and notification rules

### Scheduling Rules
- Recurrence rules must be valid for their frequency type
- Next generation dates are calculated based on recurrence patterns
- Disabled settings do not generate new audits

## Database Mapping

The domain entities map directly to the database schema defined in `docs/database.dbml`:

### Key Mapping Considerations

**Primary Keys**:
- **CUID (varchar(25))**: Used for entities frequently created client-side (Audit, AuditAnswer, Finding, etc.)
- **Integer**: Used for server-managed entities (Role, AuditTemplate, Question, etc.)

**Optimistic Concurrency**:
- `RecordVersion` field supports offline synchronization scenarios
- Incremented on each update for conflict detection

**Active Directory Integration**:
- `AdObjectGuid` fields link entities to AD objects
- `AdSyncLastDate` tracks synchronization timestamps
- `IsAdSynced` flags indicate AD-managed entities

**Audit Tracking**:
- `CreatedByUserId` and `UpdatedByUserId` track entity lifecycle
- `CreatedAt` and `UpdatedAt` provide temporal tracking
- All changes can be logged via `AuditLog` entity

**Soft Deletion**:
- `IsActive` flags enable soft deletion patterns
- Maintains referential integrity while hiding inactive records

The domain layer provides a clean, business-focused API that abstracts away these database concerns while ensuring proper mapping through the infrastructure layer.

## Additional Documentation

For more detailed information about specific aspects of the domain layer, refer to these additional documentation files:

### 📋 [Entity Relationships](docs/ENTITY_RELATIONSHIPS.md)
Comprehensive documentation of all entity relationships including:
- Detailed relationship diagrams
- Foreign key mappings
- Business rule constraints
- Referential integrity rules

### 🔧 [Business Rules](docs/BUSINESS_RULES.md)
Complete documentation of business rules implemented in the domain:
- User management and authentication rules
- Audit workflow and status transition rules
- Template and question validation rules
- Finding and corrective action lifecycle rules
- Scheduling and recurrence validation rules

### 📡 [Domain Events](docs/DOMAIN_EVENTS.md)
Detailed guide to the event-driven architecture:
- Event types and their purposes
- Event handling patterns
- Integration event mapping
- Event ordering and dependencies
- Monitoring and observability

### 🗄️ [Database Mapping](docs/DATABASE_MAPPING.md)
Technical documentation of domain-to-database mapping:
- Primary key strategies (CUID vs Integer)
- Enum mappings
- Field mappings and constraints
- Index recommendations
- Synchronization support

## Getting Started

### For Developers
1. **Understand the Domain**: Start with this README to understand the overall structure
2. **Study Relationships**: Review the [Entity Relationships](docs/ENTITY_RELATIONSHIPS.md) documentation
3. **Learn Business Rules**: Read the [Business Rules](docs/BUSINESS_RULES.md) to understand constraints
4. **Implement Features**: Use domain events as described in [Domain Events](docs/DOMAIN_EVENTS.md)

### For Database Developers
1. **Review Schema**: Study the database.dbml file in the docs folder
2. **Understand Mapping**: Read [Database Mapping](docs/DATABASE_MAPPING.md) for implementation details
3. **Plan Migrations**: Consider the migration strategies outlined in the mapping documentation

### For Architects
1. **Domain Design**: Review the DDD patterns and aggregate boundaries
2. **Event Architecture**: Study the event-driven patterns and integration points
3. **Scalability**: Consider the performance and indexing recommendations

## Contributing

When contributing to the domain layer:

1. **Follow DDD Principles**: Maintain clean separation of concerns
2. **Implement Business Rules**: Ensure all business logic is captured in the domain
3. **Use Domain Events**: Leverage events for cross-aggregate communication
4. **Update Documentation**: Keep all documentation files current with changes
5. **Write Tests**: Ensure comprehensive unit test coverage for domain logic

## Support and Questions

For questions about the domain implementation:
- Review the relevant documentation files first
- Check the business rules for constraint clarification
- Consult the entity relationships for data model questions
- Refer to the database mapping for persistence concerns
