# Business Rules Documentation

This document outlines the key business rules implemented in the HWS Audit Platform domain layer.

## Overview

Business rules are implemented throughout the domain entities to ensure data integrity, enforce business processes, and maintain system consistency. These rules are enforced at the domain level to ensure they cannot be bypassed regardless of the application layer or user interface used.

## User Management Rules

### User Role Assignment

- **Rule**: Every user must have exactly one primary role
- **Implementation**: `User.RoleId` is required and must reference a valid `Role`
- **Validation**: Role must be active and valid
- **Exception**: Throws `BusinessRuleViolationException` if role is invalid

### Active Directory Synchronization

- **Rule**: AD-synced users cannot have their core identity fields modified locally
- **Implementation**: `User.IsAdSynced` property determines edit restrictions
- **Protected Fields**: Username, Email, FirstName, LastName when AD-synced
- **Exception**: Throws `InvalidOperationDomainException` for unauthorized modifications

### User Group Membership

- **Rule**: Users can belong to multiple groups, but cannot be added to the same group twice
- **Implementation**: Composite primary key on `UserGroupMember` prevents duplicates
- **Validation**: Check for existing membership before adding
- **Exception**: Throws `BusinessRuleViolationException` for duplicate memberships

## Organizational Structure Rules

### Hierarchical Integrity

- **Rule**: Organizational hierarchy must be maintained (Location → Factory → Area → SubArea)
- **Implementation**: Required foreign key relationships with cascade restrictions
- **Validation**: Parent entities must exist and be active
- **Exception**: Throws `EntityNotFoundException` if parent doesn't exist

### Unique Naming Within Scope

- **Rule**: Names must be unique within their parent scope
- **Examples**:
  - Factory names unique within a Location
  - Area names unique within a Factory
  - SubArea names unique within an Area
- **Implementation**: Unique constraints and domain validation
- **Exception**: Throws `BusinessRuleViolationException` for naming conflicts

### Active Status Propagation

- **Rule**: Child entities cannot be active if parent is inactive
- **Implementation**: Validation checks parent active status
- **Cascade**: Deactivating parent should deactivate children
- **Exception**: Throws `BusinessRuleViolationException` for invalid status combinations

## Audit Template Rules

### Template Publishing

- **Rule**: Only published and active templates can be used for new audits
- **Implementation**: `AuditTemplate.CanBeUsed` property checks both flags
- **Validation**: Template must be `IsPublished = true` and `IsActive = true`
- **Exception**: Throws `InvalidOperationDomainException` for unpublished templates

### Template Versioning

- **Rule**: Template versions must be incremented for significant changes
- **Implementation**: `AuditTemplate.Version` field tracks design versions
- **Business Logic**: New version required when questions/structure changes significantly
- **Constraint**: Version numbers must be positive and sequential

### Question Conditional Logic

- **Rule**: Conditional questions must reference valid parent questions within the same template
- **Implementation**: `Question.ParentQuestionId` validation
- **Constraints**:
  - Parent question must exist in same template
  - Parent question must be earlier in display order
  - No circular references allowed
- **Exception**: Throws `BusinessRuleViolationException` for invalid references

### Question Type Validation

- **Rule**: Question options are only valid for SingleSelect and MultiSelect types
- **Implementation**: Validation in `Question` entity
- **Constraint**: `QuestionOption` entities only allowed for select-type questions
- **Exception**: Throws `ValidationException` for invalid question type/option combinations

### Evidence Requirements

- **Rule**: Questions marked as `EvidenceRequired` must have evidence instructions
- **Implementation**: Validation ensures `EvidenceInstructions` is provided
- **Business Logic**: Evidence timing hints guide auditor behavior
- **Exception**: Throws `ValidationException` for missing evidence instructions

## Audit Execution Rules

### Audit Assignment Rules

#### Individual Assignment

- **Rule**: Individual audits must be assigned to a specific user
- **Implementation**: `AssignmentType.Individual` requires `AssignedToUserId`
- **Validation**: User must exist and be active
- **Exception**: Throws `BusinessRuleViolationException` for missing user assignment

#### Group Assignment

- **Rule**: Group audits must be assigned to a valid user group
- **Implementation**: `AssignmentType.GroupAny` requires `AssignedToUserGroupId`
- **Claiming Logic**: Any group member can claim by setting `AssignedToUserId`
- **Constraint**: Only one user can claim a group audit
- **Exception**: Throws `InvalidOperationDomainException` for invalid claims

#### Group All Scheduled

- **Rule**: GroupAllScheduled creates individual audits for each group member
- **Implementation**: Scheduler service creates multiple `Individual` audits
- **Business Logic**: Each group member gets their own audit instance
- **Constraint**: All generated audits reference the same recurring setting

### Audit Status Workflow

- **Rule**: Audit status transitions must follow defined workflow
- **Valid Transitions**:

  ```non
  Scheduled → InProgress → Submitted → PendingManagerReview → ManagerReviewed → Closed
                     ↓         ↓                ↓
                 Cancelled  PendingCorrection   ↓
                               ↓                ↓
                            InProgress       Cancelled
  ```
  
- **Implementation**: Status change validation in `Audit` entity
- **Exception**: Throws `InvalidOperationDomainException` for invalid transitions

### Answer Validation Rules

#### Answer Type Matching

- **Rule**: Answer type must match question type
- **Implementation**: Validation ensures correct answer field is populated
- **Examples**:
  - YesNo questions require `AnswerBoolean`
  - Numeric questions require `AnswerNumeric`
  - Date questions require `AnswerDate`
- **Exception**: Throws `ValidationException` for type mismatches

#### Required Question Handling

- **Rule**: Required questions must be answered or marked as Not Applicable
- **Implementation**: `Question.IsRequired` validation
- **Not Applicable Logic**: Requires justification in `Comments` field
- **Exception**: Throws `BusinessRuleViolationException` for missing required answers

#### Evidence Attachment Rules

- **Rule**: Questions marked as `EvidenceRequired` must have attachments
- **Implementation**: Validation checks for `AuditAttachment` records
- **File Type Validation**: Attachments must match `AllowedEvidenceTypes`
- **Exception**: Throws `ValidationException` for missing or invalid evidence

### Audit Modification Rules

- **Rule**: Submitted audits cannot be modified without correction requests
- **Implementation**: Status-based edit restrictions
- **Correction Process**: Must go through `AuditCorrectionRequest` workflow
- **Exception**: Throws `InvalidOperationDomainException` for unauthorized modifications

## Finding and Corrective Action Rules

### Finding Creation

- **Rule**: Findings must be linked to specific audit answers
- **Implementation**: Required `AuditAnswerId` foreign key
- **Business Logic**: Findings represent non-conformities identified during audits
- **Severity Assignment**: Can inherit from question default or be manually set

### Finding Code Generation

- **Rule**: Finding codes should be unique and follow format (e.g., FND-2025-001)
- **Implementation**: System-generated or manually assigned
- **Format**: Configurable pattern with year and sequence number
- **Uniqueness**: Enforced at database level

### Corrective Action Assignment

- **Rule**: Corrective actions must be assigned to valid, active users
- **Implementation**: Required `AssignedToUserId` with validation
- **Due Date Logic**: Must be future date and reasonable timeframe
- **Exception**: Throws `BusinessRuleViolationException` for invalid assignments

### Corrective Action Status Workflow

- **Rule**: Corrective action status must follow defined workflow
- **Valid Transitions**:

  ```
  Assigned → InProgress → CompletedPendingVerification → VerifiedClosed
      ↓           ↓                    ↓
  Cancelled   Cancelled           Ineffective
                                      ↓
                                  Assigned (new action)
  ```

- **Implementation**: Status transition validation
- **Exception**: Throws `InvalidOperationDomainException` for invalid transitions

## Scheduling Rules

### Recurrence Rule Validation

- **Rule**: Recurrence rules must be valid for their frequency type
- **Implementation**: `RecurrenceRule.IsValid()` method
- **Frequency-Specific Rules**:
  - **Daily**: No additional parameters required
  - **Weekly**: Must specify `WeeklyDaysOfWeekMask`
  - **Monthly**: Must specify either `MonthlyDayOfMonth` OR (`MonthlyNthWeek` AND `MonthlyDayOfWeek`)
  - **Yearly**: Must specify `YearlyMonth` AND either `YearlyDayOfMonth` OR (`YearlyNthWeek` AND `YearlyDayOfWeek`)
- **Exception**: Throws `ValidationException` for invalid rule configurations

### Scheduling Generation Rules

- **Rule**: Only enabled recurring settings generate audits
- **Implementation**: `RecurringAuditSetting.IsEnabled` check
- **Generation Logic**: Based on `NextGenerationDate` calculation
- **Assignment Validation**: Must have valid assignment based on `AssignmentType`

### Date Calculation Rules

- **Rule**: Next generation dates must be calculated correctly based on recurrence rules
- **Implementation**: Complex date arithmetic in scheduler service
- **Business Logic**: Accounts for weekends, holidays, and business rules
- **Validation**: Generated dates must be in the future

## Workflow Rules

### Correction Request Rules

- **Rule**: Only one active correction request per audit
- **Implementation**: Unique constraint on `AuditCorrectionRequest.AuditId`
- **Status Logic**: Previous requests must be resolved before new ones
- **Authorization**: Only audit assignee can request corrections

### Correction Request Approval

- **Rule**: Only managers can approve/deny correction requests
- **Implementation**: Role-based authorization in application layer
- **Business Logic**: Approver cannot be the same as requester
- **Status Transitions**: Must follow defined approval workflow

### Audit Log Integrity

- **Rule**: Audit logs are immutable once created
- **Implementation**: No update/delete operations allowed
- **Data Integrity**: All significant events must be logged
- **Retention**: Logs must be retained per compliance requirements

## Data Integrity Rules

### Optimistic Concurrency Control

- **Rule**: Record versions must be checked for concurrent updates
- **Implementation**: `RecordVersion` field incremented on each update
- **Conflict Resolution**: Defined strategy for version conflicts
- **Exception**: Throws concurrency exception for version mismatches

### Soft Delete Rules

- **Rule**: Use `IsActive` flags instead of hard deletes where appropriate
- **Implementation**: Filter inactive records in queries
- **Referential Integrity**: Maintain relationships even for inactive records
- **Business Logic**: Some entities require hard deletes for compliance

### CUID Generation Rules

- **Rule**: Client-side entities must use CUIDs for offline capability
- **Implementation**: CUID generation for entities created in PWA
- **Uniqueness**: CUIDs must be globally unique across all clients
- **Synchronization**: Support for offline-first scenarios

## Validation and Error Handling

### Domain Exception Hierarchy

- **DomainException**: Base class for all domain exceptions
- **BusinessRuleViolationException**: Business rule violations
- **EntityNotFoundException**: Entity not found scenarios
- **InvalidOperationDomainException**: Invalid operations
- **ValidationException**: Data validation failures

### Validation Patterns

- **Entity Validation**: Implemented in entity constructors and methods
- **Cross-Entity Validation**: Implemented in domain services
- **Business Rule Enforcement**: Automatic validation on state changes
- **Error Messages**: Clear, business-friendly error descriptions

### Consistency Guarantees

- **Aggregate Consistency**: Enforced within aggregate boundaries
- **Cross-Aggregate Consistency**: Handled via domain events and eventual consistency
- **Transaction Boundaries**: Defined at aggregate root level
- **Compensation Logic**: For handling distributed transaction failures
