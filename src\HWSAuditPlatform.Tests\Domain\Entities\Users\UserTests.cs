using FluentAssertions;
using HWSAuditPlatform.Domain.Entities.Users;

namespace HWSAuditPlatform.Tests.Domain.Entities.Users;

public class UserTests
{
    [Fact]
    public void Constructor_ShouldSetDefaultValues()
    {
        // Act
        var user = new User();

        // Assert
        user.Username.Should().Be(string.Empty);
        user.Email.Should().Be(string.Empty);
        user.AdObjectGuid.Should().Be(string.Empty);
        user.IsActive.Should().BeTrue();
        user.IsAdSynced.Should().BeTrue();
        user.UserGroupMemberships.Should().NotBeNull().And.BeEmpty();
    }

    [Fact]
    public void FullName_WithFirstAndLastName_ShouldConcatenate()
    {
        // Arrange
        var user = new User
        {
            FirstName = "John",
            LastName = "Doe"
        };

        // Act & Assert
        user.FullName.Should().Be("<PERSON> Doe");
    }

    [Fact]
    public void FullName_WithOnlyFirstName_ShouldReturnFirstName()
    {
        // Arrange
        var user = new User
        {
            FirstName = "John",
            LastName = null
        };

        // Act & Assert
        user.FullName.Should().Be("John");
    }

    [Fact]
    public void FullName_WithOnlyLastName_ShouldReturnLastName()
    {
        // Arrange
        var user = new User
        {
            FirstName = null,
            LastName = "Doe"
        };

        // Act & Assert
        user.FullName.Should().Be("Doe");
    }

    [Fact]
    public void FullName_WithBothNamesNull_ShouldReturnEmpty()
    {
        // Arrange
        var user = new User
        {
            FirstName = null,
            LastName = null
        };

        // Act & Assert
        user.FullName.Should().BeEmpty();
    }

    [Fact]
    public void FullName_WithWhitespaceNames_ShouldTrimCorrectly()
    {
        // Arrange
        var user = new User
        {
            FirstName = "  John  ",
            LastName = "  Doe  "
        };

        // Act & Assert
        user.FullName.Should().Be("John   Doe");
    }

    [Fact]
    public void IsAdSynced_ShouldAlwaysReturnTrue()
    {
        // Arrange
        var user1 = new User();
        var user2 = new User { AdObjectGuid = "some-guid" };
        var user3 = new User { AdObjectGuid = string.Empty };

        // Act & Assert
        user1.IsAdSynced.Should().BeTrue();
        user2.IsAdSynced.Should().BeTrue();
        user3.IsAdSynced.Should().BeTrue();
    }

    [Fact]
    public void User_WithValidData_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        const string userId = "clh7ckb0x0000qh08w5t6h5zx";
        const string username = "john.doe";
        const string email = "<EMAIL>";
        const string adObjectGuid = "550e8400-e29b-41d4-a716-************";
        const int roleId = 2;
        const int factoryId = 1;

        // Act
        var user = new User
        {
            Id = userId,
            Username = username,
            FirstName = "John",
            LastName = "Doe",
            Email = email,
            RoleId = roleId,
            FactoryId = factoryId,
            AdObjectGuid = adObjectGuid,
            AdDistinguishedName = "CN=John Doe,OU=Users,DC=company,DC=com",
            AdSyncLastDate = DateTime.UtcNow
        };

        // Assert
        user.Id.Should().Be(userId);
        user.Username.Should().Be(username);
        user.Email.Should().Be(email);
        user.RoleId.Should().Be(roleId);
        user.FactoryId.Should().Be(factoryId);
        user.AdObjectGuid.Should().Be(adObjectGuid);
        user.IsActive.Should().BeTrue();
        user.IsAdSynced.Should().BeTrue();
    }

    [Fact]
    public void LastLoginDate_CanBeSetAndRetrieved()
    {
        // Arrange
        var user = new User();
        var loginDate = DateTime.UtcNow.AddHours(-2);

        // Act
        user.LastLoginDate = loginDate;

        // Assert
        user.LastLoginDate.Should().Be(loginDate);
    }

    [Fact]
    public void IsActive_CanBeSetToFalse()
    {
        // Arrange
        var user = new User();

        // Act
        user.IsActive = false;

        // Assert
        user.IsActive.Should().BeFalse();
    }

    [Fact]
    public void AdSyncLastDate_CanBeSetAndRetrieved()
    {
        // Arrange
        var user = new User();
        var syncDate = DateTime.UtcNow.AddMinutes(-30);

        // Act
        user.AdSyncLastDate = syncDate;

        // Assert
        user.AdSyncLastDate.Should().Be(syncDate);
    }

    [Fact]
    public void User_ImplementsIAggregateRoot()
    {
        // Arrange & Act
        var user = new User();

        // Assert
        user.Should().BeAssignableTo<IAggregateRoot>();
    }

    [Fact]
    public void User_InheritsFromAuditableEntity()
    {
        // Arrange & Act
        var user = new User();

        // Assert
        user.RecordVersion.Should().Be(1);
        user.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        user.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }
}
