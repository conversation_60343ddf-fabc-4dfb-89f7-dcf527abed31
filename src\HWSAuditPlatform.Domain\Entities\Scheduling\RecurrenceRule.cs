using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Domain.Entities.Scheduling;

/// <summary>
/// Represents the detailed recurrence pattern for a recurring audit setting.
/// This is an extension of RecurringAuditSettings (1-to-1 relationship).
/// Maps to the RecurrenceRules table in the database.
/// </summary>
public class RecurrenceRule : BaseEntity<string>
{
    /// <summary>
    /// Links to the parent RecurringAuditSetting (PK/FK for 1-to-1)
    /// </summary>
    [Required]
    [MaxLength(25)]
    public string RecurringAuditSettingId { get; set; } = string.Empty;

    /// <summary>
    /// Navigation property for the recurring audit setting
    /// </summary>
    public virtual RecurringAuditSetting RecurringAuditSetting { get; set; } = null!;

    /// <summary>
    /// The base frequency of recurrence (e.g., DAILY, WEEKLY, MONTHLY, YEARLY)
    /// </summary>
    public FrequencyType FrequencyType { get; set; }

    /// <summary>
    /// Frequency interval (e.g., for WEEKLY with Interval=2, it means every 2 weeks)
    /// </summary>
    public int Interval { get; set; } = 1;

    /// <summary>
    /// The date when this recurrence rule becomes active
    /// </summary>
    public DateOnly StartDate { get; set; }

    /// <summary>
    /// Optional date when this recurrence rule stops generating audits
    /// </summary>
    public DateOnly? EndDate { get; set; }

    // Weekly frequency parameters
    /// <summary>
    /// Bitmask for specific days of the week (e.g., 1=Sun, 2=Mon, ... 64=Sat). 
    /// Required for WEEKLY frequency. E.g., Monday and Wednesday = 2 | 8 = 10.
    /// </summary>
    public int? WeeklyDaysOfWeekMask { get; set; }

    // Monthly frequency parameters
    /// <summary>
    /// Specific day of the month (1-31). Used for "On day X of the month" monthly recurrence.
    /// </summary>
    public int? MonthlyDayOfMonth { get; set; }

    /// <summary>
    /// Which occurrence of the weekday (1=first, 2=second, ... 4=fourth, 5=last). 
    /// Used for "On the Nth [Weekday] of the month" monthly recurrence.
    /// </summary>
    public int? MonthlyNthWeek { get; set; }

    /// <summary>
    /// Day of week (1=Sun, 2=Mon, ..., 7=Sat). Used with MonthlyNthWeek. 
    /// Also supports 8=AnyDay, 9=Weekday, 10=WeekendDay as per UI.
    /// </summary>
    public int? MonthlyDayOfWeek { get; set; }

    // Yearly frequency parameters
    /// <summary>
    /// Specific month of the year (1-12). Required for YEARLY frequency.
    /// </summary>
    public int? YearlyMonth { get; set; }

    /// <summary>
    /// Specific day of the month (1-31). Used for "On [Month] X" yearly recurrence.
    /// </summary>
    public int? YearlyDayOfMonth { get; set; }

    /// <summary>
    /// Which occurrence of the weekday (1=first, 2=second, ... 4=fourth, 5=last). 
    /// Used for "On the Nth [Weekday] of [Month]" yearly recurrence.
    /// </summary>
    public int? YearlyNthWeek { get; set; }

    /// <summary>
    /// Day of week (1=Sun, 2=Mon, ..., 7=Sat). Used with YearlyNthWeek. 
    /// Also supports 8=AnyDay, 9=Weekday, 10=WeekendDay as per UI.
    /// </summary>
    public int? YearlyDayOfWeek { get; set; }

    /// <summary>
    /// Validates if the recurrence rule configuration is valid for the specified frequency type
    /// </summary>
    public bool IsValid()
    {
        return FrequencyType switch
        {
            FrequencyType.Daily => true, // No additional parameters required
            FrequencyType.Weekly => WeeklyDaysOfWeekMask.HasValue && WeeklyDaysOfWeekMask.Value > 0,
            FrequencyType.Monthly => (MonthlyDayOfMonth.HasValue) || (MonthlyNthWeek.HasValue && MonthlyDayOfWeek.HasValue),
            FrequencyType.Yearly => YearlyMonth.HasValue && ((YearlyDayOfMonth.HasValue) || (YearlyNthWeek.HasValue && YearlyDayOfWeek.HasValue)),
            FrequencyType.Custom => true, // Custom logic would be implemented elsewhere
            _ => false
        };
    }

    /// <summary>
    /// Gets a human-readable description of the recurrence pattern
    /// </summary>
    public string GetDescription()
    {
        var intervalText = Interval > 1 ? $"every {Interval} " : "every ";
        
        return FrequencyType switch
        {
            FrequencyType.Daily => $"{intervalText}day(s)",
            FrequencyType.Weekly => $"{intervalText}week(s) on {GetWeeklyDaysDescription()}",
            FrequencyType.Monthly => $"{intervalText}month(s) {GetMonthlyDescription()}",
            FrequencyType.Yearly => $"{intervalText}year(s) {GetYearlyDescription()}",
            FrequencyType.Custom => "custom pattern",
            _ => "unknown pattern"
        };
    }

    private string GetWeeklyDaysDescription()
    {
        if (!WeeklyDaysOfWeekMask.HasValue) return "unknown days";
        
        var days = new List<string>();
        var dayNames = new[] { "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday" };
        
        for (int i = 0; i < 7; i++)
        {
            if ((WeeklyDaysOfWeekMask.Value & (1 << i)) != 0)
            {
                days.Add(dayNames[i]);
            }
        }
        
        return string.Join(", ", days);
    }

    private string GetMonthlyDescription()
    {
        if (MonthlyDayOfMonth.HasValue)
        {
            return $"on day {MonthlyDayOfMonth.Value}";
        }
        
        if (MonthlyNthWeek.HasValue && MonthlyDayOfWeek.HasValue)
        {
            var nthText = MonthlyNthWeek.Value switch
            {
                1 => "first",
                2 => "second", 
                3 => "third",
                4 => "fourth",
                5 => "last",
                _ => MonthlyNthWeek.Value.ToString()
            };
            
            var dayText = MonthlyDayOfWeek.Value switch
            {
                1 => "Sunday", 2 => "Monday", 3 => "Tuesday", 4 => "Wednesday",
                5 => "Thursday", 6 => "Friday", 7 => "Saturday",
                8 => "any day", 9 => "weekday", 10 => "weekend day",
                _ => "unknown day"
            };
            
            return $"on the {nthText} {dayText}";
        }
        
        return "unknown pattern";
    }

    private string GetYearlyDescription()
    {
        if (!YearlyMonth.HasValue) return "unknown pattern";
        
        var monthNames = new[] { "", "January", "February", "March", "April", "May", "June",
                                "July", "August", "September", "October", "November", "December" };
        var monthName = YearlyMonth.Value >= 1 && YearlyMonth.Value <= 12 ? monthNames[YearlyMonth.Value] : "unknown month";
        
        if (YearlyDayOfMonth.HasValue)
        {
            return $"on {monthName} {YearlyDayOfMonth.Value}";
        }
        
        if (YearlyNthWeek.HasValue && YearlyDayOfWeek.HasValue)
        {
            var nthText = YearlyNthWeek.Value switch
            {
                1 => "first", 2 => "second", 3 => "third", 4 => "fourth", 5 => "last",
                _ => YearlyNthWeek.Value.ToString()
            };
            
            var dayText = YearlyDayOfWeek.Value switch
            {
                1 => "Sunday", 2 => "Monday", 3 => "Tuesday", 4 => "Wednesday",
                5 => "Thursday", 6 => "Friday", 7 => "Saturday",
                8 => "any day", 9 => "weekday", 10 => "weekend day",
                _ => "unknown day"
            };
            
            return $"on the {nthText} {dayText} of {monthName}";
        }
        
        return $"in {monthName}";
    }
}
