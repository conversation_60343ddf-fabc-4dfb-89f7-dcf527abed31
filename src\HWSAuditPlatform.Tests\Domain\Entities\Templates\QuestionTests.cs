using FluentAssertions;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Tests.Domain.Entities.Templates;

public class QuestionTests
{
    [Fact]
    public void Constructor_ShouldSetDefaultValues()
    {
        // Act
        var question = new Question();

        // Assert
        question.QuestionText.Should().Be(string.Empty);
        question.DisplayOrder.Should().Be(0);
        question.IsRequired.Should().BeTrue();
        question.EvidenceRequired.Should().BeFalse();
        question.Options.Should().NotBeNull().And.BeEmpty();
        question.ChildQuestions.Should().NotBeNull().And.BeEmpty();
    }

    [Fact]
    public void IsConditional_WithParentQuestion_ShouldReturnTrue()
    {
        // Arrange
        var question = new Question
        {
            ParentQuestionId = 1
        };

        // Act & Assert
        question.IsConditional.Should().BeTrue();
    }

    [Fact]
    public void IsConditional_WithoutParentQuestion_ShouldReturnFalse()
    {
        // Arrange
        var question = new Question
        {
            ParentQuestionId = null
        };

        // Act & Assert
        question.IsConditional.Should().BeFalse();
    }

    [Fact]
    public void HasChildQuestions_WithChildQuestions_ShouldReturnTrue()
    {
        // Arrange
        var parentQuestion = new Question();
        var childQuestion = new Question();
        parentQuestion.ChildQuestions.Add(childQuestion);

        // Act & Assert
        parentQuestion.HasChildQuestions.Should().BeTrue();
    }

    [Fact]
    public void HasChildQuestions_WithoutChildQuestions_ShouldReturnFalse()
    {
        // Arrange
        var question = new Question();

        // Act & Assert
        question.HasChildQuestions.Should().BeFalse();
    }

    [Fact]
    public void Question_WithValidData_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        const int questionId = 1;
        const int templateId = 1;
        const int groupId = 1;
        const string questionText = "Is the equipment properly maintained?";
        const QuestionType questionType = QuestionType.YesNo;
        const int displayOrder = 5;
        const bool isRequired = true;
        const decimal weight = 2.5m;
        const string helpText = "Check for visible damage and maintenance records.";
        const SeverityLevel severityLevel = SeverityLevel.Major;
        const bool evidenceRequired = true;
        const string evidenceInstructions = "Take photos of equipment condition.";
        const EvidenceTimingHint evidenceTimingHint = EvidenceTimingHint.OnSitePreferred;
        const string allowedEvidenceTypes = "image/jpeg,image/png";

        // Act
        var question = new Question
        {
            Id = questionId,
            AuditTemplateId = templateId,
            QuestionGroupId = groupId,
            QuestionText = questionText,
            QuestionType = questionType,
            DisplayOrder = displayOrder,
            IsRequired = isRequired,
            Weight = weight,
            HelpText = helpText,
            SeverityLevel = severityLevel,
            EvidenceRequired = evidenceRequired,
            EvidenceInstructions = evidenceInstructions,
            EvidenceTimingHint = evidenceTimingHint,
            AllowedEvidenceTypes = allowedEvidenceTypes
        };

        // Assert
        question.Id.Should().Be(questionId);
        question.AuditTemplateId.Should().Be(templateId);
        question.QuestionGroupId.Should().Be(groupId);
        question.QuestionText.Should().Be(questionText);
        question.QuestionType.Should().Be(questionType);
        question.DisplayOrder.Should().Be(displayOrder);
        question.IsRequired.Should().Be(isRequired);
        question.Weight.Should().Be(weight);
        question.HelpText.Should().Be(helpText);
        question.SeverityLevel.Should().Be(severityLevel);
        question.EvidenceRequired.Should().Be(evidenceRequired);
        question.EvidenceInstructions.Should().Be(evidenceInstructions);
        question.EvidenceTimingHint.Should().Be(evidenceTimingHint);
        question.AllowedEvidenceTypes.Should().Be(allowedEvidenceTypes);
    }

    [Fact]
    public void Question_WithConditionalLogic_ShouldSetParentAndTrigger()
    {
        // Arrange
        const int parentQuestionId = 1;
        const string triggerValue = "No";

        // Act
        var question = new Question
        {
            ParentQuestionId = parentQuestionId,
            TriggerAnswerValue = triggerValue
        };

        // Assert
        question.ParentQuestionId.Should().Be(parentQuestionId);
        question.TriggerAnswerValue.Should().Be(triggerValue);
        question.IsConditional.Should().BeTrue();
    }

    [Fact]
    public void Question_WithOptions_ShouldAllowOptionsCollection()
    {
        // Arrange
        var question = new Question
        {
            QuestionType = QuestionType.SingleSelect
        };

        var option1 = new QuestionOption { OptionText = "Excellent" };
        var option2 = new QuestionOption { OptionText = "Good" };
        var option3 = new QuestionOption { OptionText = "Poor" };

        // Act
        question.Options.Add(option1);
        question.Options.Add(option2);
        question.Options.Add(option3);

        // Assert
        question.Options.Should().HaveCount(3);
        question.Options.Should().Contain(option1);
        question.Options.Should().Contain(option2);
        question.Options.Should().Contain(option3);
    }

    [Fact]
    public void Question_InheritsFromBaseEntity()
    {
        // Arrange & Act
        var question = new Question();

        // Assert
        question.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        question.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Theory]
    [InlineData(QuestionType.YesNo)]
    [InlineData(QuestionType.Numeric)]
    [InlineData(QuestionType.ShortText)]
    [InlineData(QuestionType.LongText)]
    [InlineData(QuestionType.Date)]
    [InlineData(QuestionType.SingleSelect)]
    [InlineData(QuestionType.MultiSelect)]
    public void QuestionType_ShouldAcceptAllValidTypes(QuestionType questionType)
    {
        // Arrange & Act
        var question = new Question
        {
            QuestionType = questionType
        };

        // Assert
        question.QuestionType.Should().Be(questionType);
    }

    [Theory]
    [InlineData(SeverityLevel.Critical)]
    [InlineData(SeverityLevel.Major)]
    [InlineData(SeverityLevel.Minor)]
    [InlineData(SeverityLevel.Observation)]
    public void SeverityLevel_ShouldAcceptAllValidLevels(SeverityLevel severityLevel)
    {
        // Arrange & Act
        var question = new Question
        {
            SeverityLevel = severityLevel
        };

        // Assert
        question.SeverityLevel.Should().Be(severityLevel);
    }

    [Theory]
    [InlineData(EvidenceTimingHint.OnSitePreferred)]
    [InlineData(EvidenceTimingHint.LaterAllowed)]
    [InlineData(EvidenceTimingHint.AnyTime)]
    public void EvidenceTimingHint_ShouldAcceptAllValidHints(EvidenceTimingHint timingHint)
    {
        // Arrange & Act
        var question = new Question
        {
            EvidenceTimingHint = timingHint
        };

        // Assert
        question.EvidenceTimingHint.Should().Be(timingHint);
    }

    [Fact]
    public void Weight_CanBeSetToDecimalValue()
    {
        // Arrange
        var question = new Question();
        const decimal weight = 3.75m;

        // Act
        question.Weight = weight;

        // Assert
        question.Weight.Should().Be(weight);
    }

    [Fact]
    public void IsRequired_CanBeSetToFalse()
    {
        // Arrange
        var question = new Question();

        // Act
        question.IsRequired = false;

        // Assert
        question.IsRequired.Should().BeFalse();
    }

    [Fact]
    public void EvidenceRequired_CanBeSetToTrue()
    {
        // Arrange
        var question = new Question();

        // Act
        question.EvidenceRequired = true;

        // Assert
        question.EvidenceRequired.Should().BeTrue();
    }
}
