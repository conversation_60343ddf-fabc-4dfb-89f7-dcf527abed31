using FluentAssertions;
using HWSAuditPlatform.Domain.Entities.Findings;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Tests.Domain.Entities.Findings;

public class FindingTests
{
    [Fact]
    public void Constructor_ShouldSetDefaultValues()
    {
        // Act
        var finding = new Finding();

        // Assert
        finding.FindingDescription.Should().Be(string.Empty);
        finding.Status.Should().Be(FindingStatus.Open);
        finding.ReportedByUserId.Should().Be(string.Empty);
        finding.CorrectiveActions.Should().NotBeNull().And.BeEmpty();
    }

    [Fact]
    public void IsOverdue_WithDueDateInPast_ShouldReturnTrue()
    {
        // Arrange
        var finding = new Finding
        {
            DueDate = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(-1)),
            Status = FindingStatus.Open
        };

        // Act & Assert
        finding.IsOverdue.Should().BeTrue();
    }

    [Fact]
    public void IsOverdue_WithDueDateInFuture_ShouldReturnFalse()
    {
        // Arrange
        var finding = new Finding
        {
            DueDate = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(1)),
            Status = FindingStatus.Open
        };

        // Act & Assert
        finding.IsOverdue.Should().BeFalse();
    }

    [Fact]
    public void IsOverdue_WithNoDueDate_ShouldReturnFalse()
    {
        // Arrange
        var finding = new Finding
        {
            DueDate = null,
            Status = FindingStatus.Open
        };

        // Act & Assert
        finding.IsOverdue.Should().BeFalse();
    }

    [Fact]
    public void IsOverdue_WithClosedStatus_ShouldReturnFalse()
    {
        // Arrange
        var finding = new Finding
        {
            DueDate = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(-1)),
            Status = FindingStatus.Closed
        };

        // Act & Assert
        finding.IsOverdue.Should().BeFalse();
    }

    [Fact]
    public void IsOpen_WithOpenStatus_ShouldReturnTrue()
    {
        // Arrange
        var finding = new Finding
        {
            Status = FindingStatus.Open
        };

        // Act & Assert
        finding.IsOpen.Should().BeTrue();
    }

    [Fact]
    public void IsOpen_WithOtherStatus_ShouldReturnFalse()
    {
        // Arrange
        var finding = new Finding
        {
            Status = FindingStatus.Closed
        };

        // Act & Assert
        finding.IsOpen.Should().BeFalse();
    }

    [Fact]
    public void IsClosed_WithClosedStatus_ShouldReturnTrue()
    {
        // Arrange
        var finding = new Finding
        {
            Status = FindingStatus.Closed
        };

        // Act & Assert
        finding.IsClosed.Should().BeTrue();
    }

    [Fact]
    public void IsClosed_WithOtherStatus_ShouldReturnFalse()
    {
        // Arrange
        var finding = new Finding
        {
            Status = FindingStatus.Open
        };

        // Act & Assert
        finding.IsClosed.Should().BeFalse();
    }

    [Fact]
    public void CorrectiveActionCount_ShouldReturnCorrectCount()
    {
        // Arrange
        var finding = new Finding();
        finding.CorrectiveActions.Add(new CorrectiveAction());
        finding.CorrectiveActions.Add(new CorrectiveAction());
        finding.CorrectiveActions.Add(new CorrectiveAction());

        // Act & Assert
        finding.CorrectiveActionCount.Should().Be(3);
    }

    [Fact]
    public void OpenCorrectiveActionCount_ShouldReturnCorrectCount()
    {
        // Arrange
        var finding = new Finding();
        
        finding.CorrectiveActions.Add(new CorrectiveAction { Status = CorrectiveActionStatus.Assigned });
        finding.CorrectiveActions.Add(new CorrectiveAction { Status = CorrectiveActionStatus.InProgress });
        finding.CorrectiveActions.Add(new CorrectiveAction { Status = CorrectiveActionStatus.VerifiedClosed });
        finding.CorrectiveActions.Add(new CorrectiveAction { Status = CorrectiveActionStatus.Cancelled });

        // Act & Assert
        finding.OpenCorrectiveActionCount.Should().Be(2); // Only Assigned and InProgress are open
    }

    [Fact]
    public void Finding_WithValidData_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        const string findingId = "clh7ckb0x0000qh08w5t6h5zx";
        const string auditAnswerId = "clh7ckb0x0001qh08w5t6h5zy";
        const string findingCode = "FND-2025-001";
        const string description = "Equipment not properly maintained according to schedule.";
        const SeverityLevel severity = SeverityLevel.Major;
        const string rootCause = "Lack of maintenance schedule adherence.";
        const string immediateAction = "Equipment taken out of service for maintenance.";
        const string reportedByUserId = "clh7ckb0x0002qh08w5t6h5zz";
        var dueDate = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(30));

        // Act
        var finding = new Finding
        {
            Id = findingId,
            AuditAnswerId = auditAnswerId,
            FindingCode = findingCode,
            FindingDescription = description,
            FindingSeverityLevel = severity,
            RootCauseAnalysis = rootCause,
            ImmediateActionTaken = immediateAction,
            ReportedByUserId = reportedByUserId,
            DueDate = dueDate
        };

        // Assert
        finding.Id.Should().Be(findingId);
        finding.AuditAnswerId.Should().Be(auditAnswerId);
        finding.FindingCode.Should().Be(findingCode);
        finding.FindingDescription.Should().Be(description);
        finding.FindingSeverityLevel.Should().Be(severity);
        finding.RootCauseAnalysis.Should().Be(rootCause);
        finding.ImmediateActionTaken.Should().Be(immediateAction);
        finding.ReportedByUserId.Should().Be(reportedByUserId);
        finding.DueDate.Should().Be(dueDate);
    }

    [Theory]
    [InlineData(FindingStatus.Open)]
    [InlineData(FindingStatus.UnderInvestigation)]
    [InlineData(FindingStatus.PendingCorrectiveAction)]
    [InlineData(FindingStatus.PendingVerification)]
    [InlineData(FindingStatus.Closed)]
    [InlineData(FindingStatus.Rejected)]
    public void Status_ShouldAcceptAllValidStatuses(FindingStatus status)
    {
        // Arrange & Act
        var finding = new Finding
        {
            Status = status
        };

        // Assert
        finding.Status.Should().Be(status);
    }

    [Theory]
    [InlineData(SeverityLevel.Critical)]
    [InlineData(SeverityLevel.Major)]
    [InlineData(SeverityLevel.Minor)]
    [InlineData(SeverityLevel.Observation)]
    public void FindingSeverityLevel_ShouldAcceptAllValidLevels(SeverityLevel severity)
    {
        // Arrange & Act
        var finding = new Finding
        {
            FindingSeverityLevel = severity
        };

        // Assert
        finding.FindingSeverityLevel.Should().Be(severity);
    }

    [Fact]
    public void Finding_ImplementsIAggregateRoot()
    {
        // Arrange & Act
        var finding = new Finding();

        // Assert
        finding.Should().BeAssignableTo<IAggregateRoot>();
    }

    [Fact]
    public void Finding_InheritsFromAuditableEntity()
    {
        // Arrange & Act
        var finding = new Finding();

        // Assert
        finding.RecordVersion.Should().Be(1);
        finding.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        finding.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void RootCauseAnalysis_CanBeSetAndRetrieved()
    {
        // Arrange
        var finding = new Finding();
        const string rootCause = "Insufficient training on maintenance procedures.";

        // Act
        finding.RootCauseAnalysis = rootCause;

        // Assert
        finding.RootCauseAnalysis.Should().Be(rootCause);
    }

    [Fact]
    public void ImmediateActionTaken_CanBeSetAndRetrieved()
    {
        // Arrange
        var finding = new Finding();
        const string immediateAction = "Work area cordoned off and equipment tagged out.";

        // Act
        finding.ImmediateActionTaken = immediateAction;

        // Assert
        finding.ImmediateActionTaken.Should().Be(immediateAction);
    }
}
