using FluentAssertions;
using HWSAuditPlatform.Domain.Events;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Tests.Domain.Events;

public class AuditEventsTests
{
    [Fact]
    public void AuditCreatedEvent_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        const string auditId = "clh7ckb0x0000qh08w5t6h5zx";
        const int templateId = 1;
        const string userId = "clh7ckb0x0001qh08w5t6h5zy";
        const string groupId = "clh7ckb0x0002qh08w5t6h5zz";
        var scheduledDate = DateTime.UtcNow.AddDays(1);

        // Act
        var domainEvent = new AuditCreatedEvent(auditId, templateId, userId, groupId, scheduledDate);

        // Assert
        domainEvent.AuditId.Should().Be(auditId);
        domainEvent.AuditTemplateId.Should().Be(templateId);
        domainEvent.AssignedToUserId.Should().Be(userId);
        domainEvent.AssignedToUserGroupId.Should().Be(groupId);
        domainEvent.ScheduledDate.Should().Be(scheduledDate);
        domainEvent.OccurredOn.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void AuditStatusChangedEvent_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        const string auditId = "clh7ckb0x0000qh08w5t6h5zx";
        const AuditOverallStatus previousStatus = AuditOverallStatus.Scheduled;
        const AuditOverallStatus newStatus = AuditOverallStatus.InProgress;
        const string userId = "clh7ckb0x0001qh08w5t6h5zy";

        // Act
        var domainEvent = new AuditStatusChangedEvent(auditId, previousStatus, newStatus, userId);

        // Assert
        domainEvent.AuditId.Should().Be(auditId);
        domainEvent.PreviousStatus.Should().Be(previousStatus);
        domainEvent.NewStatus.Should().Be(newStatus);
        domainEvent.ChangedByUserId.Should().Be(userId);
        domainEvent.OccurredOn.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void AuditStatusChangedEvent_WithNullUserId_ShouldAcceptNull()
    {
        // Arrange
        const string auditId = "clh7ckb0x0000qh08w5t6h5zx";
        const AuditOverallStatus previousStatus = AuditOverallStatus.Scheduled;
        const AuditOverallStatus newStatus = AuditOverallStatus.InProgress;

        // Act
        var domainEvent = new AuditStatusChangedEvent(auditId, previousStatus, newStatus, null);

        // Assert
        domainEvent.ChangedByUserId.Should().BeNull();
    }

    [Fact]
    public void AuditSubmittedEvent_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        const string auditId = "clh7ckb0x0000qh08w5t6h5zx";
        const string userId = "clh7ckb0x0001qh08w5t6h5zy";
        var submittedAt = DateTime.UtcNow;

        // Act
        var domainEvent = new AuditSubmittedEvent(auditId, userId, submittedAt);

        // Assert
        domainEvent.AuditId.Should().Be(auditId);
        domainEvent.SubmittedByUserId.Should().Be(userId);
        domainEvent.SubmittedAt.Should().Be(submittedAt);
        domainEvent.OccurredOn.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void AuditAssignedEvent_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        const string auditId = "clh7ckb0x0000qh08w5t6h5zx";
        const string assignedToUserId = "clh7ckb0x0001qh08w5t6h5zy";
        const string assignedByUserId = "clh7ckb0x0002qh08w5t6h5zz";

        // Act
        var domainEvent = new AuditAssignedEvent(auditId, assignedToUserId, assignedByUserId);

        // Assert
        domainEvent.AuditId.Should().Be(auditId);
        domainEvent.AssignedToUserId.Should().Be(assignedToUserId);
        domainEvent.AssignedByUserId.Should().Be(assignedByUserId);
        domainEvent.OccurredOn.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void AuditAssignedEvent_WithNullAssignedBy_ShouldAcceptNull()
    {
        // Arrange
        const string auditId = "clh7ckb0x0000qh08w5t6h5zx";
        const string assignedToUserId = "clh7ckb0x0001qh08w5t6h5zy";

        // Act
        var domainEvent = new AuditAssignedEvent(auditId, assignedToUserId, null);

        // Assert
        domainEvent.AssignedByUserId.Should().BeNull();
    }

    [Fact]
    public void AuditStartedEvent_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        const string auditId = "clh7ckb0x0000qh08w5t6h5zx";
        const string userId = "clh7ckb0x0001qh08w5t6h5zy";
        var startedAt = DateTime.UtcNow;

        // Act
        var domainEvent = new AuditStartedEvent(auditId, userId, startedAt);

        // Assert
        domainEvent.AuditId.Should().Be(auditId);
        domainEvent.StartedByUserId.Should().Be(userId);
        domainEvent.StartedAt.Should().Be(startedAt);
        domainEvent.OccurredOn.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void AllAuditEvents_ShouldImplementIDomainEvent()
    {
        // Arrange & Act
        var auditCreated = new AuditCreatedEvent("audit1", 1, "user1", null, DateTime.UtcNow);
        var auditStatusChanged = new AuditStatusChangedEvent("audit1", AuditOverallStatus.Scheduled, AuditOverallStatus.InProgress, "user1");
        var auditSubmitted = new AuditSubmittedEvent("audit1", "user1", DateTime.UtcNow);
        var auditAssigned = new AuditAssignedEvent("audit1", "user1", "user2");
        var auditStarted = new AuditStartedEvent("audit1", "user1", DateTime.UtcNow);

        // Assert
        auditCreated.Should().BeAssignableTo<IDomainEvent>();
        auditStatusChanged.Should().BeAssignableTo<IDomainEvent>();
        auditSubmitted.Should().BeAssignableTo<IDomainEvent>();
        auditAssigned.Should().BeAssignableTo<IDomainEvent>();
        auditStarted.Should().BeAssignableTo<IDomainEvent>();
    }

    [Fact]
    public void AllAuditEvents_ShouldHaveOccurredOnProperty()
    {
        // Arrange & Act
        var auditCreated = new AuditCreatedEvent("audit1", 1, "user1", null, DateTime.UtcNow);
        var auditStatusChanged = new AuditStatusChangedEvent("audit1", AuditOverallStatus.Scheduled, AuditOverallStatus.InProgress, "user1");
        var auditSubmitted = new AuditSubmittedEvent("audit1", "user1", DateTime.UtcNow);
        var auditAssigned = new AuditAssignedEvent("audit1", "user1", "user2");
        var auditStarted = new AuditStartedEvent("audit1", "user1", DateTime.UtcNow);

        // Assert
        auditCreated.OccurredOn.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        auditStatusChanged.OccurredOn.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        auditSubmitted.OccurredOn.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        auditAssigned.OccurredOn.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        auditStarted.OccurredOn.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }
}
